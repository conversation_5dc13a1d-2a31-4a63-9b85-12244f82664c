"use client"
import { useState, useEffect } from 'react';
import Image from 'next/image';
import {
  Gift,
  Search,
  MapPin,
  Users,
  Menu,
  X,
  User
} from 'lucide-react';
import { PreferencesSelector, PreferencesModal } from '../ui/PreferencesModal';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isPreferencesOpen, setIsPreferencesOpen] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState('USD');
  const [selectedLanguage, setSelectedLanguage] = useState('EN');

  // Force light mode on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const htmlElement = document.documentElement;
      const bodyElement = document.body;

      // Remove any dark mode classes
      htmlElement.classList.remove('dark', 'dark-mode');
      htmlElement.classList.add('light', 'light-mode');
      htmlElement.setAttribute('data-theme', 'light');
      htmlElement.style.colorScheme = 'light';

      // Set localStorage to light mode
      localStorage.setItem('theme', 'light');
    }
  }, []);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { icon: Search, text: "Discover", href: "#discover" },
    { icon: Users, text: "Community", href: "#experiences" },
    { icon: MapPin, text: "Tours", href: "#tours" },
    ];

  return (
    <nav className={`fixed w-full top-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-md shadow-lg'
        : 'bg-white/80 backdrop-blur-sm'
    }`}>
      <div className="max-w-[80vw] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            <a href="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity duration-200">
              <Image
                src="/logo.png"
                alt="Association Setti Fadma Logo"
                width={40}
                height={40}
                className="w-10 h-10"
              />
              <span className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                OurikaTravels
              </span>
            </a>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navItems.map((item, index) => (
              <NavItem key={index} icon={item.icon} text={item.text} href={item.href} />
            ))}
          </div>

          {/* Right side actions */}
          <div className="flex items-center space-x-3">
            {/* Combined Currency & Language Selector */}
            <div className="hidden md:flex items-center">
              <PreferencesSelector
                currency={selectedCurrency}
                language={selectedLanguage}
                onClick={() => setIsPreferencesOpen(true)}
              />
            </div>

            {/* I am Guide button */}
            <button className="hidden sm:flex items-center space-x-2 bg-black !bg-black hover:!bg-gray-800 text-white rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg" style={{backgroundColor: '#000000', color: '#ffffff'}}>
              <User className="w-4 h-4 text-white" />
              <span className="text-white">I'm a Guide</span>
            </button>

            {/* Sign In button */}
            <button className="hidden sm:flex items-center space-x-2 bg-black !bg-black hover:!bg-gray-800 text-white rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg" style={{backgroundColor: '#000000', color: '#ffffff'}}>
              <span className="text-white">Sign In</span>
            </button>

            {/* Mobile menu button */}
            <button
              className="lg:hidden p-2 rounded-full bg-black !bg-black hover:!bg-gray-800 transition-colors duration-200"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle mobile menu"
              style={{backgroundColor: '#000000'}}
            >
              {isMenuOpen ? (
                <X className="w-6 h-6 text-white" />
              ) : (
                <Menu className="w-6 h-6 text-white" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className={`lg:hidden transition-all duration-300 ease-in-out ${
        isMenuOpen
          ? 'max-h-96 opacity-100'
          : 'max-h-0 opacity-0 overflow-hidden'
      }`}>
        <div className="bg-white/95 backdrop-blur-md border-t border-gray-200 shadow-lg">
          <div className="px-4 py-6 space-y-3">
            {navItems.map((item, index) => (
              <MobileNavItem key={index} icon={item.icon} text={item.text} href={item.href} />
            ))}

            <div className="pt-4 border-t border-gray-200">
              <div className="flex flex-col space-y-3">
                <button className="flex items-center justify-center space-x-2 w-full bg-black !bg-black hover:!bg-gray-800 text-white rounded-xl px-4 py-3 font-medium transition-all duration-200 shadow-md" style={{backgroundColor: '#000000', color: '#ffffff'}}>
                  <User className="w-5 h-5 text-white" />
                  <span className="text-white">I'm a Guide</span>
                </button>

                <button className="flex items-center justify-center space-x-2 w-full bg-black !bg-black hover:!bg-gray-800 text-white rounded-xl px-4 py-3 font-medium transition-all duration-200 shadow-md" style={{backgroundColor: '#000000', color: '#ffffff'}}>
                  <span className="text-white">Sign In</span>
                </button>

                <div className="flex items-center justify-center pt-2">
                  <PreferencesSelector
                    currency={selectedCurrency}
                    language={selectedLanguage}
                    onClick={() => setIsPreferencesOpen(true)}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Preferences Modal */}
      {isPreferencesOpen && (
        <PreferencesModal
          isOpen={isPreferencesOpen}
          onClose={() => setIsPreferencesOpen(false)}
          selectedCurrency={selectedCurrency}
          selectedLanguage={selectedLanguage}
          onCurrencyChange={setSelectedCurrency}
          onLanguageChange={setSelectedLanguage}
        />
      )}
    </nav>
  );
};

// Desktop navigation item component
const NavItem = ({ icon: Icon, text, href }) => {
  return (
    <a
      href={href}
      className="flex w-full items-center gap-2 rounded-xl px-4 py-2 text-sm font-medium text-gray-700 transition-colors duration-200 hover:bg-gray-200 hover:text-black focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-300"
    >
      <Icon className="h-4 w-4 transition-transform duration-200 group-hover:scale-110" />
      <span>{text}</span>
    </a>
  );
};



// Mobile navigation item component
const MobileNavItem = ({ icon: Icon, text, href }) => {
  return (
    <a
      href={href}
      className="flex items-center space-x-3 text-gray-700 hover:text-black rounded-xl px-4 py-3 font-medium transition-all duration-200 hover:bg-gray-200 active:bg-gray-300"
    >
      <Icon className="w-5 h-5 transition-transform duration-200 group-hover:scale-110" />
      <span>{text}</span>
    </a>
  );
};





export default Navbar;