import { MapPin, Phone, Mail, Clock, Mountain } from 'lucide-react';
import SocialMediaLinks from '../ui/SocialMediaLinks';
import NewsletterSignup from '../ui/NewsletterSignup';

const FooterSection = () => {
  const tourLinks = [
    { name: 'Ourika Valley Day Trip', href: '/tours/ourika-valley' },
    { name: 'Atlas Mountains Trek', href: '/tours/atlas-mountains' },
    { name: 'Sahara Desert Adventure', href: '/tours/sahara-desert' },
    { name: 'Berber Villages Tour', href: '/tours/berber-villages' },
    { name: 'Ouzoud Waterfalls', href: '/tours/ouzoud-waterfalls' }
  ];

  const companyLinks = [
    { name: 'About Us', href: '/about' },
    { name: 'Our Guides', href: '/guides' },
    { name: 'Sustainability', href: '/sustainability' },
    { name: 'Reviews', href: '/reviews' },
    { name: 'Blog', href: '/blog' }
  ];

  const supportLinks = [
    { name: 'Contact Us', href: '/contact' },
    { name: 'FAQ', href: '/faq' },
    { name: 'Booking Policy', href: '/booking-policy' },
    { name: 'Travel Insurance', href: '/insurance' },
    { name: 'Safety Guidelines', href: '/safety' }
  ];

  return (
    <footer className="bg-gray-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-40 h-40 border border-emerald-400 rounded-full"></div>
        <div className="absolute top-32 right-20 w-24 h-24 border border-orange-400 rounded-lg rotate-45"></div>
        <div className="absolute bottom-20 left-1/3 w-32 h-32 border border-blue-400 rounded-full"></div>
      </div>

      <div className="w-full mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 relative">
        {/* Main Footer Content */}
        <div className="py-12 sm:py-16">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 lg:gap-12">
            {/* Company Info */}
            <div className="lg:col-span-1 space-y-6">
              <div className="flex items-center gap-2">
                <Mountain className="w-8 h-8 text-emerald-400" />
                <span className="text-2xl font-bold">Ourika Travels</span>
              </div>
              
              <p className="text-gray-300 leading-relaxed">
                Your gateway to authentic Moroccan adventures in the breathtaking Ourika Valley 
                and Atlas Mountains. Experience Morocco like never before.
              </p>

              <SocialMediaLinks />

              {/* Contact Info */}
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-gray-300">
                  <MapPin className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                  <span className="text-sm">Ourika Valley, Atlas Mountains, Morocco</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Phone className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                  <span className="text-sm">+212 524 123 456</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Mail className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                  <span className="text-sm"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Clock className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                  <span className="text-sm">Daily 8:00 AM - 8:00 PM</span>
                </div>
              </div>
            </div>

            {/* Tours & Destinations */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white">Tours & Destinations</h3>
              <ul className="space-y-3">
                {tourLinks.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-gray-300 hover:text-emerald-400 transition-colors duration-200 text-sm"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Company */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white">Company</h3>
              <ul className="space-y-3">
                {companyLinks.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-gray-300 hover:text-emerald-400 transition-colors duration-200 text-sm"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>

              <div className="pt-4">
                <h4 className="text-sm font-semibold text-white mb-3">Support</h4>
                <ul className="space-y-2">
                  {supportLinks.slice(0, 3).map((link) => (
                    <li key={link.name}>
                      <a
                        href={link.href}
                        className="text-gray-300 hover:text-emerald-400 transition-colors duration-200 text-sm"
                      >
                        {link.name}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Newsletter */}
            <div className="space-y-6">
              <NewsletterSignup />
              
              {/* Certifications */}
              <div className="space-y-4">
                <h4 className="text-sm font-semibold text-white">Certified & Trusted</h4>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-emerald-600 text-white text-xs rounded-full">
                    Licensed Tour Operator
                  </span>
                  <span className="px-3 py-1 bg-blue-600 text-white text-xs rounded-full">
                    Eco-Certified
                  </span>
                  <span className="px-3 py-1 bg-orange-600 text-white text-xs rounded-full">
                    Local Partnership
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 py-6">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="text-sm text-gray-400">
              © 2024 Ourika Travels. All rights reserved.
            </div>
            <div className="flex items-center gap-6 text-sm text-gray-400">
              <a href="/privacy" className="hover:text-emerald-400 transition-colors duration-200">
                Privacy Policy
              </a>
              <a href="/terms" className="hover:text-emerald-400 transition-colors duration-200">
                Terms of Service
              </a>
              <a href="/cookies" className="hover:text-emerald-400 transition-colors duration-200">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default FooterSection;