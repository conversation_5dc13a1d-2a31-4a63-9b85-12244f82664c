"use client"
import { Search } from 'lucide-react';

interface SearchInputProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  placeholder?: string;
}

const SearchInput = ({
  searchQuery,
  onSearchChange,
  placeholder = "Places to go, things to do, hotels..."
}: SearchInputProps) => {
  return (
    <div className="relative max-w-2xl mx-auto">
      {/* Container with rounded border */}
      <div className="relative bg-gray-100 border-2 border-black rounded-full shadow-sm
                      focus-within:ring-2 focus-within:ring-green-500 focus-within:border-black
                      transition-none overflow-hidden">

        {/* Search Icon */}
        <div className="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none z-10">
          <Search className="h-5 w-5 text-gray-400" />
        </div>

        {/* Input Field */}
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          placeholder={placeholder}
          className="w-full pl-14 pr-32 py-5 text-base bg-transparent border-none rounded-full
                     focus:outline-none text-gray-900 placeholder-gray-500"
        />

        {/* Search Button */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-2">
          <button
            className="font-semibold py-3 px-8 rounded-full transition-colors duration-200 shadow-sm !bg-green-600 hover:!bg-green-700 !text-white !border-none"
            style={{
              backgroundColor: '#059669',
              color: 'white',
              border: 'none'
            }}
          >
            Search
          </button>
        </div>
      </div>
    </div>
  );
};

export default SearchInput;