"use client"
import { Search } from 'lucide-react';

interface SearchInputProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  placeholder?: string;
}

const SearchInput = ({ 
  searchQuery, 
  onSearchChange, 
  placeholder = "Places to go, things to do, hotels..." 
}: SearchInputProps) => {
  return (
    <div className="relative max-w-2xl mx-auto">
      <div className="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
        <Search className="h-5 w-5 text-gray-400" />
      </div>
      
      <input
        type="text"
        value={searchQuery}
        onChange={(e) => onSearchChange(e.target.value)}
        placeholder={placeholder}
        className="w-full pl-14 pr-32 py-5 text-base bg-gray-100 border-2 rounded-full 
                   focus:outline-none focus:ring-2 focus:ring-green-500 
                   text-gray-900 placeholder-gray-500 shadow-sm"
      />
      
      <div className="absolute inset-y-0 right-0 flex items-center pr-2">
        <button className="bg-green-500 hover:bg-green-600 text-white font-semibold 
                          py-3 px-8 rounded-full transition-colors duration-200 shadow-sm">
          Search
        </button>
      </div>
    </div>
  );
};

export default SearchInput;